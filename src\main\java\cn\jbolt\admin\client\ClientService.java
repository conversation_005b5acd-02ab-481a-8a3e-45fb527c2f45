package cn.jbolt.admin.client;

import cn.jbolt._admin.user.UserCompanyService;
import cn.jbolt.admin.emails.EmailNameCache;
import cn.jbolt.common.model.Client;
import cn.jbolt.common.model.CompanyClient;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.db.sql.Sql;
import cn.jbolt.core.poi.excel.JBoltExcel;
import cn.jbolt.core.poi.excel.JBoltExcelHeader;
import cn.jbolt.core.poi.excel.JBoltExcelSheet;
import cn.jbolt.core.poi.excel.JBoltExcelUtil;
import cn.jbolt.core.service.base.JBoltBaseService;
import cn.jbolt.extend.systemlog.ProjectSystemLogTargetType;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Okv;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;

import java.io.File;
import java.util.List;

/**
 * 客户信息管理
 *
 * @ClassName: ClientService
 * @author: 总管理
 * @date: 2024-05-15 12:59
 */
public class ClientService extends JBoltBaseService<Client> {
    private final Client dao = new Client().dao();

    @Inject
    private EmailNameCache emailNameCache;

    @Inject
    private UserCompanyService userCompanyService;

    @Override
    protected Client dao() {
        return dao;
    }

    @Override
    protected int systemLogTargetType() {
        return ProjectSystemLogTargetType.NONE.getValue();
    }

    /**
     * 后台管理数据查询
     *
     * @param pageNumber 第几页
     * @param pageSize   每页几条数据
     * @param keywords   关键词
     * @param sortColumn 排序列名
     * @param sortType   排序方式 asc desc
     * @param name       客户名字
     * @param country    国家
     * @param enable     启用/禁用
     * @return
     */
    public Page<Client> getAdminDatas(int pageNumber, int pageSize, String keywords, String sortColumn, String sortType,
            String name, String country, Boolean enable, Integer companyId) {
        Sql sql = selectSql().page(pageNumber, pageSize);
        // sql条件处理
        sql.eq("name", name);
        sql.eq("country", country);
        sql.eqBooleanToChar("enable", enable);
        // 关键词模糊查询
        sql.likeMulti(keywords, "surname", "name", "email", "nickname", "other_social", "country", "address", "remark");

        if (companyId != null) {
            Sql clientIdInSql = Sql.me(this.dbType).from("company_client").select("client_id").eq("company_id",
                    companyId);
            sql.inSql("id", clientIdInSql);
        }
        // 排序
        sql.orderBy(sortColumn, sortType);
        return paginate(sql);
    }

    /**
     * 保存
     *
     * @param client
     * @return
     */
    public Ret save(Client client, Integer companyId) {
        if (client == null || isOk(client.getId())) {
            return fail(JBoltMsg.PARAM_ERROR);
        }
        client.setSortRank(getNextSortRank());
        boolean success = client.save();
        if (success && companyId != null) {
            CompanyClient companyClient = new CompanyClient();
            companyClient.setCompanyId(companyId);
            companyClient.setClientId(client.getId());
            companyClient.save();
        }
        afterSave(client);
        UserCompanyService.refreshUserCompanyEmail(companyId);
        return ret(success);
    }

    /**
     * 更新
     *
     * @param client
     * @return
     */
    public Ret update(Client client, Integer companyId) {
        if (client == null || notOk(client.getId())) {
            return fail(JBoltMsg.PARAM_ERROR);
        }
        // 更新时需要判断数据存在
        Client dbClient = findById(client.getId());
        if (dbClient == null) {
            return fail(JBoltMsg.DATA_NOT_EXIST);
        }
        boolean success = client.update();
        if (success) {
            afterUpdate(client);
            UserCompanyService.refreshUserCompanyEmail(companyId);
            // 添加日志
            // addUpdateSystemLog(client.getId(), JBoltUserKit.getUserId(),
            // client.getName());
        }
        return ret(success);
    }

    /**
     * 删除数据后执行的回调
     *
     * @param client 要删除的model
     * @param kv     携带额外参数一般用不上
     * @return
     */
    @Override
    protected String afterDelete(Client client, Kv kv) {
        if (client != null && client.getEmail() != null) {
            String[] emails = client.getEmail().split("[,;]");
            for (String email : emails) {
                email = email.trim();
                if (!email.isEmpty()) {
                    emailNameCache.removeDisplayName(email);
                }
            }
        }
        // addDeleteSystemLog(client.getId(),
        // JBoltUserKit.getUserId(),client.getName());
        return null;
    }

    /**
     * 检测是否可以删除
     *
     * @param client model
     * @param kv     携带额外参数一般用不上
     * @return
     */
    @Override
    public String checkInUse(Client client, Kv kv) {
        // 这里用来覆盖 检测是否被其它表引用
        return null;
    }

    /**
     * 上移
     *
     * @param id
     * @return
     */
    public Ret up(Integer id) {
        Client client = findById(id);
        if (client == null) {
            return fail("数据不存在或已被删除");
        }
        Integer rank = client.getSortRank();
        if (rank == null || rank <= 0) {
            return fail("顺序需要初始化");
        }
        if (rank == 1) {
            return fail("已经是第一个");
        }
        Client upClient = findFirst(Okv.by("sort_rank", rank - 1));
        if (upClient == null) {
            return fail("顺序需要初始化");
        }
        upClient.setSortRank(rank);
        client.setSortRank(rank - 1);

        upClient.update();
        client.update();
        return SUCCESS;
    }

    /**
     * 下移
     *
     * @param id
     * @return
     */
    public Ret down(Integer id) {
        Client client = findById(id);
        if (client == null) {
            return fail("数据不存在或已被删除");
        }
        Integer rank = client.getSortRank();
        if (rank == null || rank <= 0) {
            return fail("顺序需要初始化");
        }
        int max = getCount();
        if (rank == max) {
            return fail("已经是最后已一个");
        }
        Client upClient = findFirst(Okv.by("sort_rank", rank + 1));
        if (upClient == null) {
            return fail("顺序需要初始化");
        }
        upClient.setSortRank(rank);
        client.setSortRank(rank + 1);

        upClient.update();
        client.update();
        return SUCCESS;
    }

    /**
     * 初始化排序
     */
    public Ret initSortRank() {
        List<Client> allList = findAll();
        if (allList.size() > 0) {
            for (int i = 0; i < allList.size(); i++) {
                allList.get(i).setSortRank(i + 1);
            }
            batchUpdate(allList);
        }
        // 添加日志
        // addUpdateSystemLog(null, JBoltUserKit.getUserId(), "所有数据", "的顺序:初始化所有");
        return SUCCESS;
    }

    /**
     * 生成excel导入使用的模板
     *
     * @return
     */
    public JBoltExcel getImportExcelTpl() {
        return JBoltExcel
                // 创建
                .create()
                .setSheets(
                        JBoltExcelSheet.create()
                                // 设置列映射 顺序 标题名称 不处理别名
                                .setHeaders(1, false,
                                        JBoltExcelHeader.create("姓氏", 15),
                                        JBoltExcelHeader.create("客户名字", 15),
                                        JBoltExcelHeader.create("客户邮箱", 15),
                                        JBoltExcelHeader.create("昵称", 15),
                                        JBoltExcelHeader.create("性别", 15),
                                        JBoltExcelHeader.create("主页", 15),
                                        JBoltExcelHeader.create("手机", 15),
                                        JBoltExcelHeader.create("其他社交", 15),
                                        JBoltExcelHeader.create("星级", 15),
                                        JBoltExcelHeader.create("国家", 15),
                                        JBoltExcelHeader.create("地址", 15),
                                        JBoltExcelHeader.create("备注信息", 15)));
    }

    /**
     * 读取excel文件
     */
    public Ret importExcel(File file) {
        StringBuilder errorMsg = new StringBuilder();
        JBoltExcel jBoltExcel = JBoltExcel
                // 从excel文件创建JBoltExcel实例
                .from(file)
                // 设置工作表信息
                .setSheets(
                        JBoltExcelSheet.create()
                                // 设置列映射 顺序 标题名称
                                .setHeaders(1,
                                        JBoltExcelHeader.create("surname", "姓氏"),
                                        JBoltExcelHeader.create("name", "客户名字"),
                                        JBoltExcelHeader.create("email", "客户邮箱"),
                                        JBoltExcelHeader.create("nickname", "昵称"),
                                        JBoltExcelHeader.create("gender", "性别"),
                                        JBoltExcelHeader.create("home_page", "主页"),
                                        JBoltExcelHeader.create("cell_phone", "手机"),
                                        JBoltExcelHeader.create("other_social", "其他社交"),
                                        JBoltExcelHeader.create("star", "星级"),
                                        JBoltExcelHeader.create("country", "国家"),
                                        JBoltExcelHeader.create("address", "地址"),
                                        JBoltExcelHeader.create("remark", "备注信息"))
                                // 从第三行开始读取
                                .setDataStartRow(2));
        // 从指定的sheet工作表里读取数据
        List<Client> clients = JBoltExcelUtil.readModels(jBoltExcel, 1, Client.class, errorMsg);
        if (notOk(clients)) {
            if (errorMsg.length() > 0) {
                return fail(errorMsg.toString());
            } else {
                return fail(JBoltMsg.DATA_IMPORT_FAIL_EMPTY);
            }
        }
        // 执行批量操作
        boolean success = tx(() -> {
            batchSave(clients);
            return true;
        });

        if (!success) {
            return fail(JBoltMsg.DATA_IMPORT_FAIL);
        }
        return SUCCESS;
    }

    /**
     * 生成要导出的Excel
     *
     * @return
     */
    public JBoltExcel exportExcel(List<Client> datas) {
        return JBoltExcel
                // 创建
                .create()
                // 设置工作表
                .setSheets(
                        // 设置工作表 列映射 顺序 标题名称
                        JBoltExcelSheet
                                .create()
                                // 表头映射关系
                                .setHeaders(1,
                                        JBoltExcelHeader.create("id", "ID", 15),
                                        JBoltExcelHeader.create("surname", "姓氏", 15),
                                        JBoltExcelHeader.create("name", "客户名字", 15),
                                        JBoltExcelHeader.create("email", "客户邮箱", 15),
                                        JBoltExcelHeader.create("nickname", "昵称", 15),
                                        JBoltExcelHeader.create("gender", "性别", 15),
                                        JBoltExcelHeader.create("home_page", "主页", 15),
                                        JBoltExcelHeader.create("cell_phone", "手机", 15),
                                        JBoltExcelHeader.create("other_social", "其他社交", 15),
                                        JBoltExcelHeader.create("star", "星级", 15),
                                        JBoltExcelHeader.create("country", "国家", 15),
                                        JBoltExcelHeader.create("address", "地址", 15),
                                        JBoltExcelHeader.create("remark", "备注信息", 15))
                                // 设置导出的数据源 来自于数据库查询出来的Model List
                                .setModelDatas(2, datas));
    }

    /**
     * toggle操作执行后的回调处理
     */
    @Override
    protected String afterToggleBoolean(Client client, String column, Kv kv) {
        // addUpdateSystemLog(client.getId(), JBoltUserKit.getUserId(),
        // client.getName(),"的字段["+column+"]值:"+client.get(column));
        /**
         * switch(column){
         * case "enable":
         * break;
         * }
         */
        return null;
    }

    /**
     * 保存后的回调处理
     */
    protected void afterSave(Client client) {
        refreshEmailNameCache(client);
    }

    /**
     * 更新后的回调处理
     */
    protected void afterUpdate(Client client) {
        refreshEmailNameCache(client);
    }

    private void refreshEmailNameCache(Client client) {
        if (client != null && client.getEmail() != null && client.getName() != null) {
            String[] emails = client.getEmail().split("[,;]");
            for (String email : emails) {
                email = email.trim();
                if (!email.isEmpty()) {
                    emailNameCache.updateDisplayName(email, client.getName());
                }
            }
        }
    }

    public List<Client> getClientList(Integer companyId) {
        return dao.find("select * from client where id in (select client_id from company_client where company_id=?)",
                companyId);
    }

    public List<Client> getAllClient() {
        return dao.find(
                "select c.*, cm.nick_name from client c, company_client cc, company cm where c.id=cc.client_id and cc.company_id=cm.id");
    }

    public List<Client> getAllClientNotInAccount() {
        return dao.find(
                "select c.*, cm.nick_name from client c, company_client cc, company cm where c.id=cc.client_id and cc.company_id=cm.id and c.email not in (select username from email_account)");
    }

    public Ret deleteById(Object id, Integer companyId) {
        if (companyId == null) {
            return deleteById(id, false);
        } else {
            int i = Db.delete("delete from company_client where client_id=? and company_id=?", id, companyId);
            if (i > 0) {
                return SUCCESS;
            } else {
                return fail("删除失败");
            }
        }
    }
}