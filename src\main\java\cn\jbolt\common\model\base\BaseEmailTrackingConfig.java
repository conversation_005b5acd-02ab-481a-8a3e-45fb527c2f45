package cn.jbolt.common.model.base;
import cn.jbolt.core.model.base.JBoltBaseModel;
import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 邮件跟踪配置表
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseEmailTrackingConfig<M extends BaseEmailTrackingConfig<M>> extends JBoltBaseModel<M>{
    public static final String DATASOURCE_CONFIG_NAME = "main";
    /**ID*/
    public static final String ID = "id";
    /**配置键*/
    public static final String CONFIG_KEY = "config_key";
    /**配置值*/
    public static final String CONFIG_VALUE = "config_value";
    /**配置类型*/
    public static final String CONFIG_TYPE = "config_type";
    /**配置描述*/
    public static final String DESCRIPTION = "description";
    /**配置分类*/
    public static final String CATEGORY = "category";
    /**是否为系统配置*/
    public static final String IS_SYSTEM = "is_system";
    /**是否可编辑*/
    public static final String IS_EDITABLE = "is_editable";
    /**创建时间*/
    public static final String CREATED_AT = "created_at";
    /**更新时间*/
    public static final String UPDATED_AT = "updated_at";

    /**
     * ID
     */
    public M setId(java.lang.Long id) {
        set(ID, id);
        return (M)this;
    }

    /**
     * ID
     */
    @JBoltField(name = "id", columnName = "id", type = "Long", remark = "ID", required = true, maxLength = 19, fixed = 0, order = 1)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public java.lang.Long getId() {
        return getLong(ID);
    }

    /**
     * 配置键
     */
    public M setConfigKey(java.lang.String configKey) {
        set(CONFIG_KEY, configKey);
        return (M)this;
    }

    /**
     * 配置键
     */
    @JBoltField(name = "configKey", columnName = "config_key", type = "String", remark = "配置键", required = false, maxLength = 100, fixed = 0, order = 2)
    public java.lang.String getConfigKey() {
        return getStr(CONFIG_KEY);
    }

    /**
     * 配置值
     */
    public M setConfigValue(java.lang.String configValue) {
        set(CONFIG_VALUE, configValue);
        return (M)this;
    }

    /**
     * 配置值
     */
    @JBoltField(name = "configValue", columnName = "config_value", type = "String", remark = "配置值", required = false, maxLength = 1000, fixed = 0, order = 3)
    public java.lang.String getConfigValue() {
        return getStr(CONFIG_VALUE);
    }

    /**
     * 配置类型
     */
    public M setConfigType(java.lang.String configType) {
        set(CONFIG_TYPE, configType);
        return (M)this;
    }

    /**
     * 配置类型
     */
    @JBoltField(name = "configType", columnName = "config_type", type = "String", remark = "配置类型", required = false, maxLength = 50, fixed = 0, order = 4)
    public java.lang.String getConfigType() {
        return getStr(CONFIG_TYPE);
    }

    /**
     * 配置描述
     */
    public M setDescription(java.lang.String description) {
        set(DESCRIPTION, description);
        return (M)this;
    }

    /**
     * 配置描述
     */
    @JBoltField(name = "description", columnName = "description", type = "String", remark = "配置描述", required = false, maxLength = 500, fixed = 0, order = 5)
    public java.lang.String getDescription() {
        return getStr(DESCRIPTION);
    }

    /**
     * 配置分类
     */
    public M setCategory(java.lang.String category) {
        set(CATEGORY, category);
        return (M)this;
    }

    /**
     * 配置分类
     */
    @JBoltField(name = "category", columnName = "category", type = "String", remark = "配置分类", required = false, maxLength = 100, fixed = 0, order = 6)
    public java.lang.String getCategory() {
        return getStr(CATEGORY);
    }

    /**
     * 是否为系统配置
     */
    public M setIsSystem(java.lang.Boolean isSystem) {
        set(IS_SYSTEM, isSystem);
        return (M)this;
    }

    /**
     * 是否为系统配置
     */
    @JBoltField(name = "isSystem", columnName = "is_system", type = "Boolean", remark = "是否为系统配置", required = false, maxLength = 1, fixed = 0, order = 7)
    public java.lang.Boolean getIsSystem() {
        return getBoolean(IS_SYSTEM);
    }

    /**
     * 是否可编辑
     */
    public M setIsEditable(java.lang.Boolean isEditable) {
        set(IS_EDITABLE, isEditable);
        return (M)this;
    }

    /**
     * 是否可编辑
     */
    @JBoltField(name = "isEditable", columnName = "is_editable", type = "Boolean", remark = "是否可编辑", required = false, maxLength = 1, fixed = 0, order = 8)
    public java.lang.Boolean getIsEditable() {
        return getBoolean(IS_EDITABLE);
    }

    /**
     * 创建时间
     */
    public M setCreatedAt(java.util.Date createdAt) {
        set(CREATED_AT, createdAt);
        return (M)this;
    }

    /**
     * 创建时间
     */
    @JBoltField(name = "createdAt", columnName = "created_at", type = "Date", remark = "创建时间", required = false, maxLength = 19, fixed = 0, order = 9)
    public java.util.Date getCreatedAt() {
        return getDate(CREATED_AT);
    }

    /**
     * 更新时间
     */
    public M setUpdatedAt(java.util.Date updatedAt) {
        set(UPDATED_AT, updatedAt);
        return (M)this;
    }

    /**
     * 更新时间
     */
    @JBoltField(name = "updatedAt", columnName = "updated_at", type = "Date", remark = "更新时间", required = false, maxLength = 19, fixed = 0, order = 10)
    public java.util.Date getUpdatedAt() {
        return getDate(UPDATED_AT);
    }
}
