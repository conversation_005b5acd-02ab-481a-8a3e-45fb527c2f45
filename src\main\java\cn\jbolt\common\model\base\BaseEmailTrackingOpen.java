package cn.jbolt.common.model.base;
import cn.jbolt.core.model.base.JBoltBaseModel;
import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 邮件打开事件记录表
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseEmailTrackingOpen<M extends BaseEmailTrackingOpen<M>> extends JBoltBaseModel<M>{
    public static final String DATASOURCE_CONFIG_NAME = "main";
    /**ID*/
    public static final String ID = "id";
    /**关联跟踪记录ID*/
    public static final String TRACKING_RECORD_ID = "tracking_record_id";
    /**跟踪ID*/
    public static final String TRACKING_ID = "tracking_id";
    /**打开时间*/
    public static final String OPEN_DATE = "open_date";
    /**打开者IP地址*/
    public static final String IP_ADDRESS = "ip_address";
    /**用户代理字符串*/
    public static final String USER_AGENT = "user_agent";
    /**来源页面*/
    public static final String REFERER = "referer";
    /**地理位置-国家*/
    public static final String LOCATION_COUNTRY = "location_country";
    /**地理位置-城市*/
    public static final String LOCATION_CITY = "location_city";
    /**设备类型*/
    public static final String DEVICE_TYPE = "device_type";
    /**邮件客户端*/
    public static final String EMAIL_CLIENT = "email_client";
    /**是否为唯一打开*/
    public static final String IS_UNIQUE_OPEN = "is_unique_open";
    /**创建时间*/
    public static final String CREATED_AT = "created_at";

    /**
     * ID
     */
    public M setId(java.lang.Long id) {
        set(ID, id);
        return (M)this;
    }

    /**
     * ID
     */
    @JBoltField(name = "id", columnName = "id", type = "Long", remark = "ID", required = true, maxLength = 19, fixed = 0, order = 1)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public java.lang.Long getId() {
        return getLong(ID);
    }

    /**
     * 关联跟踪记录ID
     */
    public M setTrackingRecordId(java.lang.Long trackingRecordId) {
        set(TRACKING_RECORD_ID, trackingRecordId);
        return (M)this;
    }

    /**
     * 关联跟踪记录ID
     */
    @JBoltField(name = "trackingRecordId", columnName = "tracking_record_id", type = "Long", remark = "关联跟踪记录ID", required = false, maxLength = 19, fixed = 0, order = 2)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public java.lang.Long getTrackingRecordId() {
        return getLong(TRACKING_RECORD_ID);
    }

    /**
     * 跟踪ID
     */
    public M setTrackingId(java.lang.String trackingId) {
        set(TRACKING_ID, trackingId);
        return (M)this;
    }

    /**
     * 跟踪ID
     */
    @JBoltField(name = "trackingId", columnName = "tracking_id", type = "String", remark = "跟踪ID", required = false, maxLength = 255, fixed = 0, order = 3)
    public java.lang.String getTrackingId() {
        return getStr(TRACKING_ID);
    }

    /**
     * 打开时间
     */
    public M setOpenDate(java.util.Date openDate) {
        set(OPEN_DATE, openDate);
        return (M)this;
    }

    /**
     * 打开时间
     */
    @JBoltField(name = "openDate", columnName = "open_date", type = "Date", remark = "打开时间", required = false, maxLength = 19, fixed = 0, order = 4)
    public java.util.Date getOpenDate() {
        return getDate(OPEN_DATE);
    }

    /**
     * 打开者IP地址
     */
    public M setIpAddress(java.lang.String ipAddress) {
        set(IP_ADDRESS, ipAddress);
        return (M)this;
    }

    /**
     * 打开者IP地址
     */
    @JBoltField(name = "ipAddress", columnName = "ip_address", type = "String", remark = "打开者IP地址", required = false, maxLength = 45, fixed = 0, order = 5)
    public java.lang.String getIpAddress() {
        return getStr(IP_ADDRESS);
    }

    /**
     * 用户代理字符串
     */
    public M setUserAgent(java.lang.String userAgent) {
        set(USER_AGENT, userAgent);
        return (M)this;
    }

    /**
     * 用户代理字符串
     */
    @JBoltField(name = "userAgent", columnName = "user_agent", type = "String", remark = "用户代理字符串", required = false, maxLength = 500, fixed = 0, order = 6)
    public java.lang.String getUserAgent() {
        return getStr(USER_AGENT);
    }

    /**
     * 来源页面
     */
    public M setReferer(java.lang.String referer) {
        set(REFERER, referer);
        return (M)this;
    }

    /**
     * 来源页面
     */
    @JBoltField(name = "referer", columnName = "referer", type = "String", remark = "来源页面", required = false, maxLength = 500, fixed = 0, order = 7)
    public java.lang.String getReferer() {
        return getStr(REFERER);
    }

    /**
     * 地理位置-国家
     */
    public M setLocationCountry(java.lang.String locationCountry) {
        set(LOCATION_COUNTRY, locationCountry);
        return (M)this;
    }

    /**
     * 地理位置-国家
     */
    @JBoltField(name = "locationCountry", columnName = "location_country", type = "String", remark = "地理位置-国家", required = false, maxLength = 100, fixed = 0, order = 8)
    public java.lang.String getLocationCountry() {
        return getStr(LOCATION_COUNTRY);
    }

    /**
     * 地理位置-城市
     */
    public M setLocationCity(java.lang.String locationCity) {
        set(LOCATION_CITY, locationCity);
        return (M)this;
    }

    /**
     * 地理位置-城市
     */
    @JBoltField(name = "locationCity", columnName = "location_city", type = "String", remark = "地理位置-城市", required = false, maxLength = 100, fixed = 0, order = 9)
    public java.lang.String getLocationCity() {
        return getStr(LOCATION_CITY);
    }

    /**
     * 设备类型
     */
    public M setDeviceType(java.lang.String deviceType) {
        set(DEVICE_TYPE, deviceType);
        return (M)this;
    }

    /**
     * 设备类型
     */
    @JBoltField(name = "deviceType", columnName = "device_type", type = "String", remark = "设备类型", required = false, maxLength = 50, fixed = 0, order = 10)
    public java.lang.String getDeviceType() {
        return getStr(DEVICE_TYPE);
    }

    /**
     * 邮件客户端
     */
    public M setEmailClient(java.lang.String emailClient) {
        set(EMAIL_CLIENT, emailClient);
        return (M)this;
    }

    /**
     * 邮件客户端
     */
    @JBoltField(name = "emailClient", columnName = "email_client", type = "String", remark = "邮件客户端", required = false, maxLength = 100, fixed = 0, order = 11)
    public java.lang.String getEmailClient() {
        return getStr(EMAIL_CLIENT);
    }

    /**
     * 是否为唯一打开
     */
    public M setIsUniqueOpen(java.lang.Boolean isUniqueOpen) {
        set(IS_UNIQUE_OPEN, isUniqueOpen);
        return (M)this;
    }

    /**
     * 是否为唯一打开
     */
    @JBoltField(name = "isUniqueOpen", columnName = "is_unique_open", type = "Boolean", remark = "是否为唯一打开", required = false, maxLength = 1, fixed = 0, order = 12)
    public java.lang.Boolean getIsUniqueOpen() {
        return getBoolean(IS_UNIQUE_OPEN);
    }

    /**
     * 创建时间
     */
    public M setCreatedAt(java.util.Date createdAt) {
        set(CREATED_AT, createdAt);
        return (M)this;
    }

    /**
     * 创建时间
     */
    @JBoltField(name = "createdAt", columnName = "created_at", type = "Date", remark = "创建时间", required = false, maxLength = 19, fixed = 0, order = 13)
    public java.util.Date getCreatedAt() {
        return getDate(CREATED_AT);
    }
}
