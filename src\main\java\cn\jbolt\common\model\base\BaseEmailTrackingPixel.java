package cn.jbolt.common.model.base;
import cn.jbolt.core.model.base.JBoltBaseModel;
import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 跟踪像素配置表
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseEmailTrackingPixel<M extends BaseEmailTrackingPixel<M>> extends JBoltBaseModel<M>{
    public static final String DATASOURCE_CONFIG_NAME = "main";
    /**ID*/
    public static final String ID = "id";
    /**跟踪ID*/
    public static final String TRACKING_ID = "tracking_id";
    /**像素令牌*/
    public static final String PIXEL_TOKEN = "pixel_token";
    /**像素类型*/
    public static final String PIXEL_TYPE = "pixel_type";
    /**像素尺寸*/
    public static final String PIXEL_SIZE = "pixel_size";
    /**是否激活*/
    public static final String IS_ACTIVE = "is_active";
    /**过期时间*/
    public static final String EXPIRES_AT = "expires_at";
    /**创建时间*/
    public static final String CREATED_AT = "created_at";
    /**更新时间*/
    public static final String UPDATED_AT = "updated_at";

    /**
     * ID
     */
    public M setId(java.lang.Long id) {
        set(ID, id);
        return (M)this;
    }

    /**
     * ID
     */
    @JBoltField(name = "id", columnName = "id", type = "Long", remark = "ID", required = true, maxLength = 19, fixed = 0, order = 1)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public java.lang.Long getId() {
        return getLong(ID);
    }

    /**
     * 跟踪ID
     */
    public M setTrackingId(java.lang.String trackingId) {
        set(TRACKING_ID, trackingId);
        return (M)this;
    }

    /**
     * 跟踪ID
     */
    @JBoltField(name = "trackingId", columnName = "tracking_id", type = "String", remark = "跟踪ID", required = false, maxLength = 255, fixed = 0, order = 2)
    public java.lang.String getTrackingId() {
        return getStr(TRACKING_ID);
    }

    /**
     * 像素令牌
     */
    public M setPixelToken(java.lang.String pixelToken) {
        set(PIXEL_TOKEN, pixelToken);
        return (M)this;
    }

    /**
     * 像素令牌
     */
    @JBoltField(name = "pixelToken", columnName = "pixel_token", type = "String", remark = "像素令牌", required = false, maxLength = 255, fixed = 0, order = 3)
    public java.lang.String getPixelToken() {
        return getStr(PIXEL_TOKEN);
    }

    /**
     * 像素类型
     */
    public M setPixelType(java.lang.Integer pixelType) {
        set(PIXEL_TYPE, pixelType);
        return (M)this;
    }

    /**
     * 像素类型
     */
    @JBoltField(name = "pixelType", columnName = "pixel_type", type = "Integer", remark = "像素类型", required = false, maxLength = 10, fixed = 0, order = 4)
    public java.lang.Integer getPixelType() {
        return getInt(PIXEL_TYPE);
    }

    /**
     * 像素尺寸
     */
    public M setPixelSize(java.lang.String pixelSize) {
        set(PIXEL_SIZE, pixelSize);
        return (M)this;
    }

    /**
     * 像素尺寸
     */
    @JBoltField(name = "pixelSize", columnName = "pixel_size", type = "String", remark = "像素尺寸", required = false, maxLength = 50, fixed = 0, order = 5)
    public java.lang.String getPixelSize() {
        return getStr(PIXEL_SIZE);
    }

    /**
     * 是否激活
     */
    public M setIsActive(java.lang.Boolean isActive) {
        set(IS_ACTIVE, isActive);
        return (M)this;
    }

    /**
     * 是否激活
     */
    @JBoltField(name = "isActive", columnName = "is_active", type = "Boolean", remark = "是否激活", required = false, maxLength = 1, fixed = 0, order = 6)
    public java.lang.Boolean getIsActive() {
        return getBoolean(IS_ACTIVE);
    }

    /**
     * 过期时间
     */
    public M setExpiresAt(java.util.Date expiresAt) {
        set(EXPIRES_AT, expiresAt);
        return (M)this;
    }

    /**
     * 过期时间
     */
    @JBoltField(name = "expiresAt", columnName = "expires_at", type = "Date", remark = "过期时间", required = false, maxLength = 19, fixed = 0, order = 7)
    public java.util.Date getExpiresAt() {
        return getDate(EXPIRES_AT);
    }

    /**
     * 创建时间
     */
    public M setCreatedAt(java.util.Date createdAt) {
        set(CREATED_AT, createdAt);
        return (M)this;
    }

    /**
     * 创建时间
     */
    @JBoltField(name = "createdAt", columnName = "created_at", type = "Date", remark = "创建时间", required = false, maxLength = 19, fixed = 0, order = 8)
    public java.util.Date getCreatedAt() {
        return getDate(CREATED_AT);
    }

    /**
     * 更新时间
     */
    public M setUpdatedAt(java.util.Date updatedAt) {
        set(UPDATED_AT, updatedAt);
        return (M)this;
    }

    /**
     * 更新时间
     */
    @JBoltField(name = "updatedAt", columnName = "updated_at", type = "Date", remark = "更新时间", required = false, maxLength = 19, fixed = 0, order = 9)
    public java.util.Date getUpdatedAt() {
        return getDate(UPDATED_AT);
    }
}
