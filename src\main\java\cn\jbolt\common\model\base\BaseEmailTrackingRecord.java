package cn.jbolt.common.model.base;
import cn.jbolt.core.model.base.JBoltBaseModel;
import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 邮件跟踪记录表
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseEmailTrackingRecord<M extends BaseEmailTrackingRecord<M>> extends JBoltBaseModel<M>{
    public static final String DATASOURCE_CONFIG_NAME = "main";
    /**ID*/
    public static final String ID = "id";
    /**唯一跟踪ID*/
    public static final String TRACKING_ID = "tracking_id";
    /**关联的邮件消息ID*/
    public static final String MESSAGE_ID = "message_id";
    /**关联的email_messages表ID*/
    public static final String EMAIL_MESSAGE_ID = "email_message_id";
    /**发件人邮箱*/
    public static final String FROM_ADDRESS = "from_address";
    /**收件人邮箱*/
    public static final String TO_ADDRESS = "to_address";
    /**抄送邮箱*/
    public static final String CC_ADDRESS = "cc_address";
    /**邮件主题*/
    public static final String SUBJECT = "subject";
    /**发送时间*/
    public static final String SENT_DATE = "sent_date";
    /**投递状态*/
    public static final String DELIVERY_STATUS = "delivery_status";
    /**是否启用跟踪*/
    public static final String TRACKING_ENABLED = "tracking_enabled";
    /**跟踪像素URL*/
    public static final String PIXEL_URL = "pixel_url";
    /**总打开次数*/
    public static final String TOTAL_OPENS = "total_opens";
    /**唯一打开次数*/
    public static final String UNIQUE_OPENS = "unique_opens";
    /**首次打开时间*/
    public static final String FIRST_OPEN_DATE = "first_open_date";
    /**最后打开时间*/
    public static final String LAST_OPEN_DATE = "last_open_date";
    /**创建时间*/
    public static final String CREATED_AT = "created_at";
    /**更新时间*/
    public static final String UPDATED_AT = "updated_at";

    /**
     * ID
     */
    public M setId(java.lang.Long id) {
        set(ID, id);
        return (M)this;
    }

    /**
     * ID
     */
    @JBoltField(name = "id", columnName = "id", type = "Long", remark = "ID", required = true, maxLength = 19, fixed = 0, order = 1)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public java.lang.Long getId() {
        return getLong(ID);
    }

    /**
     * 唯一跟踪ID
     */
    public M setTrackingId(java.lang.String trackingId) {
        set(TRACKING_ID, trackingId);
        return (M)this;
    }

    /**
     * 唯一跟踪ID
     */
    @JBoltField(name = "trackingId", columnName = "tracking_id", type = "String", remark = "唯一跟踪ID", required = false, maxLength = 255, fixed = 0, order = 2)
    public java.lang.String getTrackingId() {
        return getStr(TRACKING_ID);
    }

    /**
     * 关联的邮件消息ID
     */
    public M setMessageId(java.lang.String messageId) {
        set(MESSAGE_ID, messageId);
        return (M)this;
    }

    /**
     * 关联的邮件消息ID
     */
    @JBoltField(name = "messageId", columnName = "message_id", type = "String", remark = "关联的邮件消息ID", required = false, maxLength = 255, fixed = 0, order = 3)
    public java.lang.String getMessageId() {
        return getStr(MESSAGE_ID);
    }

    /**
     * 关联的email_messages表ID
     */
    public M setEmailMessageId(java.lang.Long emailMessageId) {
        set(EMAIL_MESSAGE_ID, emailMessageId);
        return (M)this;
    }

    /**
     * 关联的email_messages表ID
     */
    @JBoltField(name = "emailMessageId", columnName = "email_message_id", type = "Long", remark = "关联的email_messages表ID", required = false, maxLength = 19, fixed = 0, order = 4)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public java.lang.Long getEmailMessageId() {
        return getLong(EMAIL_MESSAGE_ID);
    }

    /**
     * 发件人邮箱
     */
    public M setFromAddress(java.lang.String fromAddress) {
        set(FROM_ADDRESS, fromAddress);
        return (M)this;
    }

    /**
     * 发件人邮箱
     */
    @JBoltField(name = "fromAddress", columnName = "from_address", type = "String", remark = "发件人邮箱", required = false, maxLength = 255, fixed = 0, order = 5)
    public java.lang.String getFromAddress() {
        return getStr(FROM_ADDRESS);
    }

    /**
     * 收件人邮箱
     */
    public M setToAddress(java.lang.String toAddress) {
        set(TO_ADDRESS, toAddress);
        return (M)this;
    }

    /**
     * 收件人邮箱
     */
    @JBoltField(name = "toAddress", columnName = "to_address", type = "String", remark = "收件人邮箱", required = false, maxLength = 255, fixed = 0, order = 6)
    public java.lang.String getToAddress() {
        return getStr(TO_ADDRESS);
    }

    /**
     * 抄送邮箱
     */
    public M setCcAddress(java.lang.String ccAddress) {
        set(CC_ADDRESS, ccAddress);
        return (M)this;
    }

    /**
     * 抄送邮箱
     */
    @JBoltField(name = "ccAddress", columnName = "cc_address", type = "String", remark = "抄送邮箱", required = false, maxLength = 255, fixed = 0, order = 7)
    public java.lang.String getCcAddress() {
        return getStr(CC_ADDRESS);
    }

    /**
     * 邮件主题
     */
    public M setSubject(java.lang.String subject) {
        set(SUBJECT, subject);
        return (M)this;
    }

    /**
     * 邮件主题
     */
    @JBoltField(name = "subject", columnName = "subject", type = "String", remark = "邮件主题", required = false, maxLength = 500, fixed = 0, order = 8)
    public java.lang.String getSubject() {
        return getStr(SUBJECT);
    }

    /**
     * 发送时间
     */
    public M setSentDate(java.util.Date sentDate) {
        set(SENT_DATE, sentDate);
        return (M)this;
    }

    /**
     * 发送时间
     */
    @JBoltField(name = "sentDate", columnName = "sent_date", type = "Date", remark = "发送时间", required = false, maxLength = 19, fixed = 0, order = 9)
    public java.util.Date getSentDate() {
        return getDate(SENT_DATE);
    }

    /**
     * 投递状态
     */
    public M setDeliveryStatus(java.lang.Integer deliveryStatus) {
        set(DELIVERY_STATUS, deliveryStatus);
        return (M)this;
    }

    /**
     * 投递状态
     */
    @JBoltField(name = "deliveryStatus", columnName = "delivery_status", type = "Integer", remark = "投递状态", required = false, maxLength = 10, fixed = 0, order = 10)
    public java.lang.Integer getDeliveryStatus() {
        return getInt(DELIVERY_STATUS);
    }

    /**
     * 是否启用跟踪
     */
    public M setTrackingEnabled(java.lang.Boolean trackingEnabled) {
        set(TRACKING_ENABLED, trackingEnabled);
        return (M)this;
    }

    /**
     * 是否启用跟踪
     */
    @JBoltField(name = "trackingEnabled", columnName = "tracking_enabled", type = "Boolean", remark = "是否启用跟踪", required = false, maxLength = 1, fixed = 0, order = 11)
    public java.lang.Boolean getTrackingEnabled() {
        return getBoolean(TRACKING_ENABLED);
    }

    /**
     * 跟踪像素URL
     */
    public M setPixelUrl(java.lang.String pixelUrl) {
        set(PIXEL_URL, pixelUrl);
        return (M)this;
    }

    /**
     * 跟踪像素URL
     */
    @JBoltField(name = "pixelUrl", columnName = "pixel_url", type = "String", remark = "跟踪像素URL", required = false, maxLength = 500, fixed = 0, order = 12)
    public java.lang.String getPixelUrl() {
        return getStr(PIXEL_URL);
    }

    /**
     * 总打开次数
     */
    public M setTotalOpens(java.lang.Integer totalOpens) {
        set(TOTAL_OPENS, totalOpens);
        return (M)this;
    }

    /**
     * 总打开次数
     */
    @JBoltField(name = "totalOpens", columnName = "total_opens", type = "Integer", remark = "总打开次数", required = false, maxLength = 10, fixed = 0, order = 13)
    public java.lang.Integer getTotalOpens() {
        return getInt(TOTAL_OPENS);
    }

    /**
     * 唯一打开次数
     */
    public M setUniqueOpens(java.lang.Integer uniqueOpens) {
        set(UNIQUE_OPENS, uniqueOpens);
        return (M)this;
    }

    /**
     * 唯一打开次数
     */
    @JBoltField(name = "uniqueOpens", columnName = "unique_opens", type = "Integer", remark = "唯一打开次数", required = false, maxLength = 10, fixed = 0, order = 14)
    public java.lang.Integer getUniqueOpens() {
        return getInt(UNIQUE_OPENS);
    }

    /**
     * 首次打开时间
     */
    public M setFirstOpenDate(java.util.Date firstOpenDate) {
        set(FIRST_OPEN_DATE, firstOpenDate);
        return (M)this;
    }

    /**
     * 首次打开时间
     */
    @JBoltField(name = "firstOpenDate", columnName = "first_open_date", type = "Date", remark = "首次打开时间", required = false, maxLength = 19, fixed = 0, order = 15)
    public java.util.Date getFirstOpenDate() {
        return getDate(FIRST_OPEN_DATE);
    }

    /**
     * 最后打开时间
     */
    public M setLastOpenDate(java.util.Date lastOpenDate) {
        set(LAST_OPEN_DATE, lastOpenDate);
        return (M)this;
    }

    /**
     * 最后打开时间
     */
    @JBoltField(name = "lastOpenDate", columnName = "last_open_date", type = "Date", remark = "最后打开时间", required = false, maxLength = 19, fixed = 0, order = 16)
    public java.util.Date getLastOpenDate() {
        return getDate(LAST_OPEN_DATE);
    }

    /**
     * 创建时间
     */
    public M setCreatedAt(java.util.Date createdAt) {
        set(CREATED_AT, createdAt);
        return (M)this;
    }

    /**
     * 创建时间
     */
    @JBoltField(name = "createdAt", columnName = "created_at", type = "Date", remark = "创建时间", required = false, maxLength = 19, fixed = 0, order = 17)
    public java.util.Date getCreatedAt() {
        return getDate(CREATED_AT);
    }

    /**
     * 更新时间
     */
    public M setUpdatedAt(java.util.Date updatedAt) {
        set(UPDATED_AT, updatedAt);
        return (M)this;
    }

    /**
     * 更新时间
     */
    @JBoltField(name = "updatedAt", columnName = "updated_at", type = "Date", remark = "更新时间", required = false, maxLength = 19, fixed = 0, order = 18)
    public java.util.Date getUpdatedAt() {
        return getDate(UPDATED_AT);
    }
}
