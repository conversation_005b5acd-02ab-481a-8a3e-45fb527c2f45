package cn.jbolt.common.model.base;
import cn.jbolt.core.model.base.JBoltBaseModel;
import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 邮件跟踪统计汇总表
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseEmailTrackingStats<M extends BaseEmailTrackingStats<M>> extends JBoltBaseModel<M>{
    public static final String DATASOURCE_CONFIG_NAME = "main";
    /**ID*/
    public static final String ID = "id";
    /**统计日期*/
    public static final String DATE_KEY = "date_key";
    /**发件人邮箱*/
    public static final String FROM_ADDRESS = "from_address";
    /**总发送数*/
    public static final String TOTAL_SENT = "total_sent";
    /**总投递数*/
    public static final String TOTAL_DELIVERED = "total_delivered";
    /**总打开数*/
    public static final String TOTAL_OPENS = "total_opens";
    /**唯一打开数*/
    public static final String UNIQUE_OPENS = "unique_opens";
    /**打开率*/
    public static final String OPEN_RATE = "open_rate";
    /**投递率*/
    public static final String DELIVERY_RATE = "delivery_rate";
    /**创建时间*/
    public static final String CREATED_AT = "created_at";
    /**更新时间*/
    public static final String UPDATED_AT = "updated_at";

    /**
     * ID
     */
    public M setId(java.lang.Long id) {
        set(ID, id);
        return (M)this;
    }

    /**
     * ID
     */
    @JBoltField(name = "id", columnName = "id", type = "Long", remark = "ID", required = true, maxLength = 19, fixed = 0, order = 1)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public java.lang.Long getId() {
        return getLong(ID);
    }

    /**
     * 统计日期
     */
    public M setDateKey(java.util.Date dateKey) {
        set(DATE_KEY, dateKey);
        return (M)this;
    }

    /**
     * 统计日期
     */
    @JBoltField(name = "dateKey", columnName = "date_key", type = "Date", remark = "统计日期", required = false, maxLength = 19, fixed = 0, order = 2)
    public java.util.Date getDateKey() {
        return getDate(DATE_KEY);
    }

    /**
     * 发件人邮箱
     */
    public M setFromAddress(java.lang.String fromAddress) {
        set(FROM_ADDRESS, fromAddress);
        return (M)this;
    }

    /**
     * 发件人邮箱
     */
    @JBoltField(name = "fromAddress", columnName = "from_address", type = "String", remark = "发件人邮箱", required = false, maxLength = 255, fixed = 0, order = 3)
    public java.lang.String getFromAddress() {
        return getStr(FROM_ADDRESS);
    }

    /**
     * 总发送数
     */
    public M setTotalSent(java.lang.Integer totalSent) {
        set(TOTAL_SENT, totalSent);
        return (M)this;
    }

    /**
     * 总发送数
     */
    @JBoltField(name = "totalSent", columnName = "total_sent", type = "Integer", remark = "总发送数", required = false, maxLength = 10, fixed = 0, order = 4)
    public java.lang.Integer getTotalSent() {
        return getInt(TOTAL_SENT);
    }

    /**
     * 总投递数
     */
    public M setTotalDelivered(java.lang.Integer totalDelivered) {
        set(TOTAL_DELIVERED, totalDelivered);
        return (M)this;
    }

    /**
     * 总投递数
     */
    @JBoltField(name = "totalDelivered", columnName = "total_delivered", type = "Integer", remark = "总投递数", required = false, maxLength = 10, fixed = 0, order = 5)
    public java.lang.Integer getTotalDelivered() {
        return getInt(TOTAL_DELIVERED);
    }

    /**
     * 总打开数
     */
    public M setTotalOpens(java.lang.Integer totalOpens) {
        set(TOTAL_OPENS, totalOpens);
        return (M)this;
    }

    /**
     * 总打开数
     */
    @JBoltField(name = "totalOpens", columnName = "total_opens", type = "Integer", remark = "总打开数", required = false, maxLength = 10, fixed = 0, order = 6)
    public java.lang.Integer getTotalOpens() {
        return getInt(TOTAL_OPENS);
    }

    /**
     * 唯一打开数
     */
    public M setUniqueOpens(java.lang.Integer uniqueOpens) {
        set(UNIQUE_OPENS, uniqueOpens);
        return (M)this;
    }

    /**
     * 唯一打开数
     */
    @JBoltField(name = "uniqueOpens", columnName = "unique_opens", type = "Integer", remark = "唯一打开数", required = false, maxLength = 10, fixed = 0, order = 7)
    public java.lang.Integer getUniqueOpens() {
        return getInt(UNIQUE_OPENS);
    }

    /**
     * 打开率
     */
    public M setOpenRate(java.math.BigDecimal openRate) {
        set(OPEN_RATE, openRate);
        return (M)this;
    }

    /**
     * 打开率
     */
    @JBoltField(name = "openRate", columnName = "open_rate", type = "BigDecimal", remark = "打开率", required = false, maxLength = 10, fixed = 2, order = 8)
    public java.math.BigDecimal getOpenRate() {
        return getBigDecimal(OPEN_RATE);
    }

    /**
     * 投递率
     */
    public M setDeliveryRate(java.math.BigDecimal deliveryRate) {
        set(DELIVERY_RATE, deliveryRate);
        return (M)this;
    }

    /**
     * 投递率
     */
    @JBoltField(name = "deliveryRate", columnName = "delivery_rate", type = "BigDecimal", remark = "投递率", required = false, maxLength = 10, fixed = 2, order = 9)
    public java.math.BigDecimal getDeliveryRate() {
        return getBigDecimal(DELIVERY_RATE);
    }

    /**
     * 创建时间
     */
    public M setCreatedAt(java.util.Date createdAt) {
        set(CREATED_AT, createdAt);
        return (M)this;
    }

    /**
     * 创建时间
     */
    @JBoltField(name = "createdAt", columnName = "created_at", type = "Date", remark = "创建时间", required = false, maxLength = 19, fixed = 0, order = 10)
    public java.util.Date getCreatedAt() {
        return getDate(CREATED_AT);
    }

    /**
     * 更新时间
     */
    public M setUpdatedAt(java.util.Date updatedAt) {
        set(UPDATED_AT, updatedAt);
        return (M)this;
    }

    /**
     * 更新时间
     */
    @JBoltField(name = "updatedAt", columnName = "updated_at", type = "Date", remark = "更新时间", required = false, maxLength = 19, fixed = 0, order = 11)
    public java.util.Date getUpdatedAt() {
        return getDate(UPDATED_AT);
    }
}
