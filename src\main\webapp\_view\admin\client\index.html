#@jboltLayout()
#define main()
#set(pageId=RandomUtil.random(6))
<div class="jbolt_page" data-key="#(pmkey??)">
    <div class="jbolt_page_title">
        <div class="row">
            <div class="col">
                <form class="form-inline" id="Client_form_#(pageId)">
                    <input type="text" class="form-control" data-autocomplete autocomplete="off" maxlength="255"
                           placeholder="请选择客户"
                           data-url="/admin/company/companyOptions"
                           data-hidden-input="companyId"
                           data-text-attr="name"
                           data-value-attr="id"
                           value="#(company.name??)"
                    />
                    <input type="hidden" autocomplete="off" class="form-control" name="companyId" id="companyId" value="#(companyId??)"/>
                    <input type="text" autocomplete="off" class="form-control" placeholder="输入关键字搜索"
                           name="keywords" value=""/>
                    <div class="btn-group text-center mx-1">
                        <button type="submit" class="btn btn-outline-primary"><i class="fa fa-search"></i> 查询</button>
                        <button type="button" onclick="form.reset();refreshJBoltTable(this);"
                                class="btn btn-outline-secondary"><i class="fa fa-reply-all"></i> 重置
                        </button>
                    </div>
                </form>
            </div>
            <div class="col-sm-auto text-right">
                <button data-dialogbtn class="btn btn-outline-primary btn-sm" data-url="admin/client/add?companyId=#(companyId??)"
                        data-handler="jboltTablePageToFirst" data-area="800,600" tooltip data-title="新增客户信息管理">
                    <i class="fa fa-plus"></i></button>
                <button class="btn btn-outline-info btn-sm" onclick="refreshJBoltTable(this)" tooltip
                        data-title="刷新数据"><i class="fa fa-refresh"></i></button>
                <button data-ajaxbtn data-confirm="确认初始化顺序吗？" data-handler="refreshJBoltTable" tooltip
                        data-title="初始化排序" data-url="admin/client/initSortRank"
                        class="btn btn-outline-info btn-sm"><i class="fa fa-sort"></i></button>
                <button data-dialogbtn data-btn="close" tooltip data-area="600,400" data-handler="jboltTablePageToFirst"
                        data-title="导入Excel数据" data-url="admin/client/initImportExcel"
                        class="btn btn-outline-primary btn-sm"><i class="fa fa-upload"></i></button>
                <div class="btn-group btn-group-sm">
                    <button tooltip data-title="导出Excel(查询结果)" data-form="Client_form_#(pageId)"
                            class="btn btn-outline-primary btn-sm" data-downloadbtn
                            data-url="admin/client/exportExcelByForm"><i class="fa fa-download"></i></button>
                </div>
            </div>
        </div>
    </div>
    <div class="jbolt_page_content">
        <!-- 定义JBoltTable使用的数据渲染模板 -->
        <textarea class="jb_tpl_box" id="Client_tpl_#(pageId)">
{@each datas as data,index}
<tr data-id="${data.id}">
<td>${pageNumber,pageSize,index | rownum}</td>
<td>${data.name}</td>
<td>${data.email}</td>
<td>${data.nickname}</td>
<td>${data.gender}</td>
<td>${data.homePage}</td>
<td>${data.cellPhone}</td>
<td>${data.otherSocialName}</td>
<td>${data.star}</td>
<td>${data.country}</td>
<td>${data.address}</td>
<td>${data.remark}</td>
<td><img data-switchbtn data-confirm="确定切换启用/禁用？" data-value="${data.enable}"  data-handler="refreshJBoltTable"  data-url="admin/client/toggleEnable/${data.id}"/></td>
<td>$${data.createTime|date_ymdhms}</td>
<td>$${data.updateTime|date_ymdhms}</td>
<td>
	<a class="jbolt_table_editbtn" href="admin/client/edit/${data.id}" data-handler="refreshJBoltTable" data-area="800,600" data-title="编辑客户信息管理"><i class="fa fa-edit"></i></a>
	<a class="jbolt_table_delbtn" href="admin/client/delete?id=${data.id}&companyId=#(companyId??)" data-handler="refreshJBoltTable" ><i class="fa fa-trash c-danger"></i></a>
	<a href="admin/client/up/${data.id}" data-ajaxbtn data-loading="执行中 " tooltip data-title="上移" data-handler="moveUp"   class="jbolt_table_btn"><i class="fa fa-arrow-up c-info"></i></a>
	<a href="admin/client/down/${data.id}" data-ajaxbtn data-loading="执行中 " tooltip data-title="下移" data-handler="moveDown"  class="jbolt_table_btn"><i class="fa fa-arrow-down c-info"></i></a>
</td>
</tr>
{@/each}
</textarea>
        <table class="jbolt_table jbolt_main_table table-center"
               data-jbolttable
               data-width="fill"
               data-height="fill"
               data-ajax="true"
               data-conditions-form="Client_form_#(pageId)"
               data-url="admin/client/datas"
               data-rowtpl="Client_tpl_#(pageId)"
               data-copy-to-excel="false"
               data-page="Client_page"
               data-column-resize="true"
               data-fixed-columns-left="1,2"
               data-fixed-columns-right="-1"
               data-sortable-columns="name,cell_phone,country,create_time,update_time"
               data-sort="#((sortColumn&&sortType)?(sortColumn+':'+sortType):'')"
               data-default-sort-column="sort_rank"
               data-row-click-active="true"
        >
            <thead class="fw-normal">
            <tr>
                <th data-width="60" data-column="index">序号</th>
                <th data-width="150" data-column="name">客户名字</th>
                <th data-width="220" data-column="email">客户邮箱</th>
                <th data-width="150" data-column="nickname">昵称</th>
                <th data-width="150" data-column="gender">性别</th>
                <th data-width="150" data-column="home_page">主页</th>
                <th data-width="150" data-column="cell_phone">手机</th>
                <th data-width="150" data-column="other_social">其他社交</th>
                <th data-width="150" data-column="star">星级</th>
                <th data-width="150" data-column="country">国家</th>
                <th data-width="150" data-column="address">地址</th>
                <th data-width="150" data-column="remark">备注信息</th>
                <th data-width="100" data-column="enable">启用/禁用</th>
                <th data-width="180" data-column="create_time">创建时间</th>
                <th data-width="180" data-column="update_time">更新时间</th>
                <th data-width="120">操作</th>
            </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
</script>
#end
