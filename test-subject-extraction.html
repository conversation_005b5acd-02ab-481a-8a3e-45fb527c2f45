<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件主题提取测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .input {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .output {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
            border-left: 4px solid #4caf50;
        }
        .error {
            background: #ffe8e8;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
            border-left: 4px solid #f44336;
        }
        .success {
            color: #4caf50;
            font-weight: bold;
        }
        .fail {
            color: #f44336;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>邮件主题提取功能测试</h1>
    <p>测试修复后的 <code>extractSubjectFromTranslation</code> 函数</p>

    <div id="test-results"></div>

    <script>
        // 修复后的 extractSubjectFromTranslation 函数
        function extractSubjectFromTranslation(content) {
            if (!content) return null;

            // 定义多种可能的主题模式
            const patterns = [
                // 邮件主题：xxx
                /邮件主题[：:]\s*(.+?)(?:\n|$|<br|<\/p>|<\/div>)/i,
                // 主题：xxx
                /主题[：:]\s*(.+?)(?:\n|$|<br|<\/p>|<\/div>)/i,
                // Subject: xxx
                /Subject[：:]\s*(.+?)(?:\n|$|<br|<\/p>|<\/div>)/i,
                // 标题：xxx
                /标题[：:]\s*(.+?)(?:\n|$|<br|<\/p>|<\/div>)/i,
                // 邮件标题：xxx
                /邮件标题[：:]\s*(.+?)(?:\n|$|<br|<\/p>|<\/div>)/i
            ];

            for (const pattern of patterns) {
                const match = content.match(pattern);
                if (match && match[1]) {
                    let subject = match[1].trim();

                    // 清理HTML标签
                    subject = subject.replace(/<[^>]*>/g, '');

                    // 清理HTML实体
                    subject = subject
                        .replace(/&nbsp;/g, ' ')
                        .replace(/&amp;/g, '&')
                        .replace(/&lt;/g, '<')
                        .replace(/&gt;/g, '>')
                        .replace(/&quot;/g, '"')
                        .replace(/&#39;/g, "'");

                    // 去除多余的空格
                    subject = subject.replace(/\s+/g, ' ').trim();

                    // 如果主题不为空，返回它（移除长度限制，避免截断长主题）
                    if (subject && subject.length > 0) {
                        // 如果主题过长，可以适当截断但保留更多内容
                        if (subject.length > 500) {
                            subject = subject.substring(0, 500) + '...';
                        }
                        return subject;
                    }
                }
            }

            return null;
        }

        // 测试用例
        const testCases = [
            {
                name: "短主题测试（< 200字符）",
                input: "邮件主题：询价请求",
                expected: "询价请求",
                description: "测试短主题是否正常工作"
            },
            {
                name: "中等长度主题测试（200-500字符）",
                input: "邮件主题：" + "这是一个非常长的邮件主题，".repeat(20) + "结束",
                expected: "这是一个非常长的邮件主题，".repeat(20) + "结束",
                description: "测试中等长度主题不再被截断"
            },
            {
                name: "超长主题测试（> 500字符）",
                input: "邮件主题：" + "这是一个超级长的邮件主题，".repeat(40) + "结束",
                expected: ("这是一个超级长的邮件主题，".repeat(40) + "结束").substring(0, 500) + "...",
                description: "测试超长主题被适当截断"
            },
            {
                name: "包含HTML标签的主题",
                input: "邮件主题：<b>重要</b>询价请求<br>",
                expected: "重要询价请求",
                description: "测试HTML标签被正确清理"
            },
            {
                name: "包含HTML实体的主题",
                input: "邮件主题：询价&amp;报价请求&nbsp;-&nbsp;紧急",
                expected: "询价&报价请求 - 紧急",
                description: "测试HTML实体被正确转换"
            },
            {
                name: "不同模式匹配测试",
                input: "Subject: Product Inquiry Request",
                expected: "Product Inquiry Request",
                description: "测试英文Subject模式匹配"
            },
            {
                name: "空主题测试",
                input: "邮件主题：",
                expected: null,
                description: "测试空主题返回null"
            },
            {
                name: "无匹配模式测试",
                input: "这是一段没有主题标识的文本内容",
                expected: null,
                description: "测试无匹配模式返回null"
            }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = extractSubjectFromTranslation(testCase.input);
                const passed = result === testCase.expected;
                
                if (passed) passCount++;

                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                testDiv.innerHTML = `
                    <h3>测试 ${index + 1}: ${testCase.name} <span class="${passed ? 'success' : 'fail'}">${passed ? '✓ 通过' : '✗ 失败'}</span></h3>
                    <p><strong>描述：</strong>${testCase.description}</p>
                    <div><strong>输入：</strong></div>
                    <div class="input">${escapeHtml(testCase.input)}</div>
                    <div><strong>期望输出：</strong></div>
                    <div class="output">${testCase.expected ? escapeHtml(testCase.expected) : 'null'}</div>
                    <div><strong>实际输出：</strong></div>
                    <div class="${passed ? 'output' : 'error'}">${result ? escapeHtml(result) : 'null'}</div>
                    ${!passed ? `<div class="error"><strong>错误：</strong>输出不匹配</div>` : ''}
                `;
                resultsDiv.appendChild(testDiv);
            });

            // 添加总结
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'test-case';
            summaryDiv.innerHTML = `
                <h3>测试总结</h3>
                <p><strong>通过：</strong><span class="success">${passCount}</span> / <strong>总计：</strong>${totalCount}</p>
                <p><strong>成功率：</strong>${((passCount / totalCount) * 100).toFixed(1)}%</p>
                ${passCount === totalCount ? '<p class="success">🎉 所有测试通过！主题截断问题已修复。</p>' : '<p class="fail">❌ 部分测试失败，需要进一步检查。</p>'}
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
