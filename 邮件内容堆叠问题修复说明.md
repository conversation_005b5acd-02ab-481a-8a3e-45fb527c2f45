# 邮件内容堆叠问题修复说明

## 问题描述

在邮件查看页面中，邮件内容存在堆叠显示不清楚的问题，主要表现为：

1. **段落间距不足**：段落之间没有足够的间距，导致内容紧贴在一起
2. **嵌套元素重叠**：span、div、p等嵌套元素没有适当的间距，造成内容堆叠
3. **表格内容紧密**：表格单元格内容过于紧密，影响可读性
4. **引用内容不清晰**：邮件引用部分的显示效果不佳
5. **行高过小**：文本行高不足，影响阅读体验

## 修复方案

### 1. CSS样式改进

#### 段落间距优化
```css
/* 改善段落间距，避免内容堆叠 */
#originalContent p + p {
    margin-top: 0.8em !important; /* 增加段落间距 */
}

/* 为div元素添加适当间距 */
#originalContent div + div {
    margin-top: 0.5em !important;
}

/* 为span元素在换行时添加间距 */
#originalContent span + span {
    margin-left: 0.2em !important;
}
```

#### 嵌套元素处理
```css
/* 修复重叠的嵌套内容 - 改善显示效果 */
#originalContent span span, 
#originalContent div div, 
#originalContent p p {
    display: inline !important;
    margin: 0 0.1em 0 0 !important; /* 添加右边距，避免内容紧贴 */
    padding: 0 !important;
}

/* 特殊处理嵌套div，确保块级元素正确显示 */
#originalContent div div {
    display: block !important;
    margin: 0.2em 0 !important; /* 为嵌套div添加垂直间距 */
}
```

#### 表格样式改进
```css
/* 确保所有单元格内容可见 - 改善表格显示 */
#originalContent td, #originalContent th {
    word-break: break-word !important;
    max-width: 100% !important;
    overflow: visible !important; /* 改为可见，避免内容被隐藏 */
    text-overflow: clip !important; /* 改为clip，避免省略号 */
    white-space: normal !important;
    vertical-align: top !important; /* 确保内容顶部对齐 */
    padding: 8px !important; /* 增加内边距，避免内容紧贴 */
    border: 1px solid #ddd !important; /* 添加边框，清晰分隔 */
    line-height: 1.4 !important; /* 增加行高 */
}
```

#### 引用内容优化
```css
/* 调整blockquote样式，确保引用内容清晰可见 */
#originalContent blockquote,
#originalContent .quoted-text {
    margin: 15px 0 !important; /* 增加上下间距 */
    padding: 15px 20px !important; /* 增加内边距 */
    border-left: 4px solid #007bff !important;
    background-color: #f8f9fa !important;
    color: #333 !important;
    border-radius: 4px !important; /* 添加圆角 */
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important; /* 添加阴影 */
    line-height: 1.6 !important; /* 增加行高 */
}
```

### 2. JavaScript功能改进

#### 新增内容间距改善函数
```javascript
/**
 * 改善内容间距，避免内容堆叠
 * @param {HTMLElement} container - 要处理的容器
 */
function improveContentSpacing(container) {
    // 为所有文本节点添加适当的间距
    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    let textNode;
    while (textNode = walker.nextNode()) {
        const parent = textNode.parentElement;
        if (parent && textNode.textContent.trim()) {
            // 确保父元素有适当的行高
            if (!parent.style.lineHeight) {
                parent.style.lineHeight = '1.5';
            }
        }
    }
    
    // 为相邻的行内元素添加间距
    const inlineElements = container.querySelectorAll('span, a, strong, b, em, i');
    inlineElements.forEach(el => {
        const nextSibling = el.nextElementSibling;
        if (nextSibling && window.getComputedStyle(nextSibling).display === 'inline') {
            if (!el.style.marginRight) {
                el.style.marginRight = '0.2em';
            }
        }
    });
}
```

#### 改进嵌套元素处理
```javascript
// 为嵌套span添加适当的间距，避免内容堆叠
if (!span.style.marginRight) {
    span.style.marginRight = '0.2em';
}

// 如果span内容为空或只有空格，添加最小宽度
if (!span.textContent.trim()) {
    span.style.minWidth = '0.5em';
    span.style.display = 'inline-block';
}
```

#### 优化样式清理逻辑
```javascript
// 添加改善的布局样式，避免内容堆叠
// 不再强制设置margin为0，而是根据元素类型设置适当间距
if (el.tagName.toLowerCase() === 'p') {
    el.style.marginBottom = '0.5em';
} else if (el.tagName.toLowerCase() === 'div') {
    el.style.marginBottom = '0.3em';
} else if (['span', 'a', 'strong', 'b', 'em', 'i'].includes(el.tagName.toLowerCase())) {
    el.style.marginRight = '0.1em';
}

el.style.lineHeight = '1.5'; // 增加行高，改善可读性
```

## 修复效果

经过以上修复，邮件内容显示将得到以下改善：

1. **段落间距合理**：段落之间有足够的间距，内容层次清晰
2. **嵌套元素分离**：嵌套的span、div、p元素不再重叠，各自有适当间距
3. **表格内容清晰**：表格单元格有足够的内边距和边框，内容易于阅读
4. **引用内容突出**：引用部分有明显的视觉区分，背景色和边框清晰
5. **行高适中**：文本行高增加到1.5，提高阅读舒适度
6. **整体布局优化**：内容不再堆叠，各部分有清晰的视觉分隔

## 测试建议

1. 测试包含多层嵌套HTML结构的邮件
2. 测试包含表格的邮件内容
3. 测试包含引用回复的邮件
4. 测试纯文本和HTML混合的邮件
5. 测试不同邮件客户端发送的邮件格式

通过这些修复，邮件内容的可读性和用户体验将得到显著提升。
