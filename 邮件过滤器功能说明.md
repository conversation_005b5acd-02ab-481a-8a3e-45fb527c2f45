# 邮件过滤器功能实现说明

## 功能概述

本次实现为邮件系统添加了两个主要功能：
1. **收件文件夹过滤器** - 支持按收件箱、发件箱、草稿箱进行过滤
2. **公用预设过滤器** - 自动创建"客户邮件"和"未跟进"两个常用预设

## 1. 收件文件夹过滤器

### 前端实现

#### 1.1 搜索过滤区域
在 `dashboard.html` 的搜索过滤区域添加了文件夹选择器：
```html
<div class="col-md-2 col-sm-6 mb-1">
    <select class="form-control form-control-sm" id="filter-folder-#(pageId)">
        <option value="">选择文件夹...</option>
        <option value="inbox">收件箱</option>
        <option value="sent">发件箱</option>
        <option value="draft">草稿箱</option>
    </select>
</div>
```

#### 1.2 快速过滤按钮
在快速过滤面板添加了文件夹快速过滤按钮：
```html
<div class="filter-button-group mb-2">
    <span class="filter-group-label">收件文件夹：</span>
    <button class="btn btn-sm btn-outline-primary quick-filter-btn"
            data-filter="folder_type" data-value="inbox">
        <i class="fa fa-inbox mr-1"></i>收件箱
    </button>
    <button class="btn btn-sm btn-outline-success quick-filter-btn"
            data-filter="folder_type" data-value="sent">
        <i class="fa fa-paper-plane mr-1"></i>发件箱
    </button>
    <button class="btn btn-sm btn-outline-warning quick-filter-btn"
            data-filter="folder_type" data-value="draft">
        <i class="fa fa-edit mr-1"></i>草稿箱
    </button>
</div>
```

#### 1.3 JavaScript支持
更新了以下JavaScript函数：
- `getCurrentFilters()` - 添加了 `folderFilter` 参数获取
- `fetchEmailsWithFilter()` - 添加了 `folderFilter` 参数传递
- `getFilterDisplayText()` - 添加了文件夹过滤器的显示文本映射

### 后端实现

#### 1.4 控制器层
在 `AdminIndexController.getDashboardEmailsWithFilter()` 方法中：
```java
String folderFilter = getPara("folderFilter");
if (StrKit.notBlank(folderFilter)) {
    params.set("folderFilter", folderFilter);
}
```

#### 1.5 服务层过滤逻辑（已修正）
在 `EmailMessagesService.getDashboardEmails()` 方法中实现了基于 `folder_name` 字段的过滤逻辑：

**收件箱过滤：**
```sql
AND (s.folder_name = 'INBOX' OR s.folder_name NOT IN ('Sent', 'Drafts', 'Draft', 'Outbox', 'Sent Items', '已发送', '草稿箱', '发件箱'))
```

**发件箱过滤：**
```sql
AND (s.folder_name IN ('Sent', 'Outbox', 'Sent Items', '已发送', '发件箱') OR 
     (s.folder_name != 'Drafts' AND s.folder_name != 'Draft' AND s.folder_name != '草稿箱' AND 
      s.from_address IN (SELECT username FROM email_account WHERE id IN 
                        (SELECT email_account_id FROM vw_user_email WHERE user_id = ?))))
```

**草稿箱过滤：**
```sql
AND (s.folder_name IN ('Drafts', 'Draft', '草稿箱') OR s.is_draft = 1)
```

#### 1.6 快速过滤支持
在 `buildFilterCondition()` 方法中添加了对 `folder_type` 过滤器的支持，使用相同的逻辑。

## 2. 公用预设过滤器

### 2.1 自动初始化
在页面初始化时调用 `initDefaultPresets()` 函数，自动创建以下预设：

**客户邮件预设：**
```javascript
{
    name: '客户邮件',
    filters: JSON.stringify({
        'is_customer_email': 'true'
    }),
    logic: 'or',
    isDefault: false
}
```

**未跟进预设：**
```javascript
{
    name: '未跟进',
    filters: JSON.stringify({
        'follow_status': 'not_followed'
    }),
    logic: 'or',
    isDefault: false
}
```

### 2.2 智能创建逻辑
- 检查预设是否已存在，避免重复创建
- 使用Promise确保异步操作的正确执行
- 创建完成后自动刷新预设列表

## 3. 过滤逻辑修正说明

### 3.1 原始实现问题
最初的实现基于 `from_address` 字段判断邮件类型：
- 收件箱：`from_address NOT IN (用户邮箱账户)`
- 发件箱：`from_address IN (用户邮箱账户)`

### 3.2 修正后的实现
根据用户反馈，修正为基于 `folder_name` 字段的判断：
- **收件箱**：`folder_name = 'INBOX'` 或其他非发送文件夹
- **发件箱**：`folder_name` 为已发送文件夹（'Sent', 'Outbox', 'Sent Items' 等）
- **草稿箱**：`folder_name` 为草稿文件夹（'Drafts', 'Draft'）或 `is_draft = 1`

### 3.3 多语言支持
考虑到不同邮箱服务商可能使用不同的文件夹名称（中文/英文），过滤条件包含了常见的变体：
- 英文：'Sent', 'Drafts', 'Outbox', 'Sent Items'
- 中文：'已发送', '草稿箱', '发件箱'

## 4. 技术特点

### 4.1 兼容性
- 与现有过滤系统完全兼容
- 支持与其他过滤条件的组合使用
- 支持AND/OR逻辑运算

### 4.2 性能优化
- 在SQL层面进行过滤，避免Java内存操作
- 使用索引优化查询性能
- 支持分页和排序

### 4.3 用户体验
- 提供下拉选择器和快速按钮两种操作方式
- 按钮状态实时反馈
- 过滤条件显示清晰

## 5. 使用方法

1. **文件夹过滤**：
   - 在搜索区域选择文件夹类型，点击"筛选"按钮
   - 或在快速过滤面板直接点击文件夹按钮

2. **预设过滤**：
   - 在预设下拉菜单中选择"客户邮件"或"未跟进"
   - 预设会在页面首次加载时自动创建

## 6. 测试验证

创建了测试页面 `test_email_filters.html` 用于验证UI效果和交互逻辑，确保所有功能按预期工作。
